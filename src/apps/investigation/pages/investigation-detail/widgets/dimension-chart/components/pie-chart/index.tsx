import LegendChart from '@/components/legend-chart';
import { defineComponent, PropType, ref } from 'vue';

const SERIES_TEXT_STYLE = {
  color: '#666',
  rich: {
    text1: {
      color: '#666',
      lineHeight: 18,
    },
    text2: {
      color: '#128BED',
      lineHeight: 22,
    },
  },
};

const TITLE_TEXT_STYLE = {
  rich: {
    description: {
      fontSize: 14,
      lineHeight: 20,
      color: '#666666',
    },
    percentage: {
      fontSize: 24,
      lineHeight: 22,
      fontWeight: 'bold',
      fontFamily: 'D-DIN, sans-serif',
      color: '#128bed',
      padding: [15, 0, 0, 0],
    },
  },
};

const getRiskChartOption = (item: Record<string, number>, chartTitle) => {
  const formatData = [
    {
      value: item.D - item.N,
      name: '其他',
      color: '#EEEEEE',
      textStyle: SERIES_TEXT_STYLE,
      itemStyle: { normal: { color: '#EEEEEE' }, emphasis: { color: '#EEEEEE' } },
    },
    {
      value: item.N,
      name: 'test1',
      color: '#5B8FF9',
      textStyle: SERIES_TEXT_STYLE,
      itemStyle: { normal: { color: '#5B8FF9' }, emphasis: { color: '#5B8FF9' } },
    },
  ];

  const title = `${chartTitle}`.replace(/(.{10})/g, '$1\n');

  return {
    title: {
      y: 'center',
      x: 150,
      text: `{description|${title}(${item.N})}\n{percentage|${item.ratio}%}`,
      textAlign: 'left',
      textStyle: TITLE_TEXT_STYLE,
      show: true,
    },
    tooltip: {
      trigger: 'item',
      show: false,
    },
    color: formatData.filter((d) => d.value)?.map((d) => d.color),
    legend: {
      show: false,
      data: [],
    },
    series: [
      {
        name: item.key,
        type: 'pie',
        radius: [37, 60],
        center: [70, '50%'],
        hoverOffset: 5,
        data: formatData,
        avoidLabelOverlap: false,
        label: { show: false },
        cursor: 'default', // 如果不需要 hover 效果，设置为 `default` 来获得更合理的用户交互
        emphasis: {
          disabled: true,
        },
      },
    ],
  };
};

const PieChart = defineComponent({
  name: 'PieChart',
  props: {
    value: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    chartTitle: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    // 饼图
    const option = ref(getRiskChartOption(props.value[0], props.chartTitle));

    return {
      option,
    };
  },
  render() {
    const { option } = this;
    return (
      <LegendChart
        height={'298px'}
        width={'320px'}
        option={option}
        legend={option.legend.data as any}
        showCount={false}
        class="px-[10px]"
      />
    );
  },
});

export default PieChart;
