.container{
    padding-left: 57px;

    .tableDes{
        display: flex;
        flex-direction: column;
        height: 100%;
        align-items: center;

        .icon{
            color: #128bed;
            font-size: 20px;
            transform: rotate(180deg);
        }

        .des{
            writing-mode: vertical-rl; /* 从右向左的垂直排列 */
            text-orientation: upright; /* 保持文本直立 */
            letter-spacing: 4px;
            transform: translateX(-1px);
        }
    }

    :global{
        .ant-table-thead{
            display: none;
        }

        .ant-table-bordered table .ant-table-tbody > tr > td:not(:last-child){
            border-right: 1px solid #e8e8e8;
        }

        .ant-table-bordered table .ant-table-tbody > tr > td:first-child{
            border-left: 1px solid #e8e8e8;
        }

        .ant-table-bordered table .ant-table-tbody > tr:first-child > td:nth-child(2),
        .ant-table-bordered table .ant-table-tbody > tr:not(:first-child) > td:first-child{
            border-left: 0;
        }

        .ant-table-bordered .ant-table-thead > tr > th:last-child{
            border-right: 0;
        }
    }
}
