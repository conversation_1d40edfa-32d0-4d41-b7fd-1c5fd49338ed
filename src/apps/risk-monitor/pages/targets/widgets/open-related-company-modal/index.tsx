import { computed, defineComponent, onMounted, ref } from 'vue';
import { Button, message } from 'ant-design-vue';

import { createPromiseDialog } from '@/components/promise-dialogs';
import QModal from '@/components/global/q-modal/q-modal';
import { convertSortStructure } from '@/utils/data-type/convert-sort-structure';
import { monitor } from '@/shared/services';
import { Permission } from '@/config/permissions.config';

import { RelatedColoumns } from '../../config/search-config';
import { useSearchCompanies } from '../../hooks/use-search-companies';
import SearchResult from '../search-result';

export const RelatedCompanyModal = defineComponent({
  name: 'RelatedCompanyModal',
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const visible = ref(false);
    const resRef = ref<any>(null);

    const options = computed(() => props.params.options || {});

    const tableColumns = computed(() =>
      options.value.disableAction ? RelatedColoumns.slice(0, RelatedColoumns.length - 1) : RelatedColoumns
    );

    const filterValues = ref({
      filters: {
        companyId: props.params.record.companyId,
        monitorGroupId: props.params.record.monitorGroupId,
        searchType: 2,
      },
    });

    const searchCompanies = useSearchCompanies(filterValues, monitor.searchRelatedCompanies);

    const selectRows = ref([]);

    // 是否有删除操作
    const hasDelete = ref(false);

    /** 移除公司 */
    const handleRemoveCompanies = async (removedCompanies: { companyIdRelated: string; monitorGroupId: string }[]) => {
      if (removedCompanies?.length === 0) {
        await monitor.removeAllRelated({
          companyId: props.params.record.companyId,
          monitorGroupId: props.params.record.monitorGroupId,
        });
      } else {
        await monitor.removeCompanyFromGroup({
          deleteMonitors: removedCompanies.map((item) => ({
            companyId: item.companyIdRelated,
            monitorGroupId: +item.monitorGroupId,
          })),
        });
      }

      message.success('删除成功');
      hasDelete.value = true;
      selectRows.value = [];
      await searchCompanies.search();
      resRef?.value?.resetSelect();
    };

    const handleClose = () => {
      visible.value = false;
      emit('resolve', hasDelete.value);
    };

    onMounted(() => {
      visible.value = true;
      searchCompanies.search();
    });

    return {
      visible,
      resRef,
      searchCompanies,
      hasDelete,
      selectRows,
      options,
      tableColumns,
      handleRemoveCompanies,
      handleClose,
    };
  },
  render() {
    return (
      <QModal visible={this.visible} size={'extra-large'} onCancel={this.handleClose} footer={!this.options.disableAction && undefined}>
        <div slot="title" style="width: calc(100% - 80px)">
          <a
            href={`/embed/companyDetail?keyNo=${this.params.record.companyId}&title=${this.params.record.companyName}`}
            target="_blank"
            domPropsInnerHTML={this.params.record.companyName}
          ></a>
          -监控关联方企业
        </div>
        <SearchResult
          ref="resRef"
          class="modal-table"
          style={{ margin: '-15px' }}
          isLoading={this.searchCompanies.isLoading.value}
          rowKey={'companyIdRelated'}
          columns={this.tableColumns}
          showExtra={false}
          emptyMinHeight={'404px'}
          scroll={{ x: false, y: 315 }}
          operatorList={this.params?.operatorList}
          dataSource={this.searchCompanies.data.value?.data ?? []}
          pagination={this.searchCompanies.pagination.value}
          selectable={!this.options.disableAction}
          on={{
            removeItems: (items) => {
              this.handleRemoveCompanies(items);
            },
            changePage: (pageIndex: number, pageSize: number) => {
              this.searchCompanies.search({ pageIndex, pageSize });
            },
            sorterChange: (sorterData) => {
              this.searchCompanies.sortInfo.value = convertSortStructure(sorterData);
              this.searchCompanies.search();
            },
            selectItems: (data) => {
              this.selectRows = data;
            },
            beforeRouterChange: this.handleClose,
          }}
          extraSlots={{
            relatedTypeDescList: (data) => {
              if (!data?.length) {
                return '-';
              }
              return (
                <div>
                  {data.map((item) => {
                    return <div>{item}</div>;
                  })}
                </div>
              );
            },
          }}
        />
        <div slot="footer">
          <Button
            data-testid="delete-all-btn"
            v-permission={[Permission.MONITOR_ENTERPRISE_DELETE]}
            disabled={!this.searchCompanies.pagination.value.total}
            onClick={() => this.handleRemoveCompanies([])}
          >
            删除全部
            {this.searchCompanies.pagination.value.total > 0 ? this.searchCompanies.pagination.value.total : null}
          </Button>
          <Button
            data-testid="delete-selected-btn"
            v-permission={[Permission.MONITOR_ENTERPRISE_DELETE]}
            type="primary"
            disabled={!this.selectRows?.length}
            onClick={() => this.handleRemoveCompanies(this.selectRows)}
          >
            删除选中{this.selectRows?.length > 0 ? this.selectRows?.length : null}
          </Button>
        </div>
      </QModal>
    );
  },
});

export const OpenRelatedCompanyModal = createPromiseDialog(RelatedCompanyModal);
