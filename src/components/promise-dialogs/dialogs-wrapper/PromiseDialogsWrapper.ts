import { defineComponent, h, onBeforeUnmount, watch } from 'vue';
import { useRoute } from 'vue-router/composables';

import { closeAllDialogs } from '../close';
import { dialogsData, rejectDialog, resolveDialog, wrapperExists } from './store';

export default defineComponent({
  name: 'PromiseDialogsWrapper',
  props: {
    unmountDelay: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    if (wrapperExists.value) {
      console.error('PromiseDialogsWrapper instance already exists');
    }

    wrapperExists.value = true;

    // 监听路由变化，当路由发生变化时关闭所有弹窗
    const route = useRoute();
    watch(
      () => route.path,
      () => {
        console.log('changed');

        closeAllDialogs('Route changed');
      }
    );

    onBeforeUnmount(() => {
      wrapperExists.value = false;

      dialogsData.value = {};
    });

    return () =>
      h(
        'div',
        Object.keys(dialogsData.value).map((id) => {
          const value = dialogsData.value[id];
          const component = value.component;
          const params = value.params;

          const resolve = (result: unknown, unmountDelay?: number) =>
            resolveDialog(id, result, unmountDelay || value.unmountDelay || props.unmountDelay);

          const reject = (error: unknown, unmountDelay?: number) =>
            rejectDialog(id, error, unmountDelay || value.unmountDelay || props.unmountDelay);

          return h(component as any, {
            key: id as any,
            props: {
              params,
            },
            on: {
              resolve,
              reject,
            },
          });
        })
      );
  },
});
