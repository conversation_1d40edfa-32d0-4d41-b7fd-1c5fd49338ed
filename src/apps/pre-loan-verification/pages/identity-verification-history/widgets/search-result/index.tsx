import { computed, defineComponent, PropType, ref, unref } from 'vue';
import { differenceBy, uniqBy, escape } from 'lodash';
import { Button } from 'ant-design-vue';

import QRichTable from '@/components/global/q-rich-table';
import QCard from '@/components/global/q-card';
import SearchCount from '@/components/search-count';

import QEntityLink from '@/components/global/q-entity-link';

const SearchResult = defineComponent({
  name: 'SearchResult',
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    dataSource: {
      type: Array,
      default: () => [],
    },
    pagination: {
      type: Object,
      default: () => ({}),
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    operatorList: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    searchKey: {
      type: String,
      default: '',
    },
  },
  setup(props, { emit }) {
    const selection = ref<any[]>([]);
    const rowSelection = computed(() => ({
      selectedRowKeys: unref(selection).map((item) => item[props.rowKey]),
      checkStrictly: false,
      onChange: (rowKeys: (string | number)[], rows: Record<string, any>[]) => {
        selection.value = uniqBy([...differenceBy(unref(selection), props.dataSource, props.rowKey), ...rows], props.rowKey);
        emit('updateSelection', unref(selection));
      },
    }));
    return {
      selection,
      rowSelection,
    };
  },
  render() {
    const pagigation: any = {
      ...this.pagination,
      onChange: (current, pageSize) => {
        this.$emit('changePage', current, pageSize);
      },
      onShowSizeChange: (current, pageSize) => {
        this.$emit('changePage', current, pageSize);
      },
    };
    return (
      <QCard bodyStyle={{ padding: '15px' }}>
        <div slot="title">
          <SearchCount slot="title" showSelects={true} total={pagigation.total} loading={this.isLoading} selectedIds={this.selection} />
        </div>
        <QRichTable
          rowKey={this.rowKey}
          tableLayout="fixed"
          showIndex={false}
          columns={this.columns}
          dataSource={this.dataSource}
          customScroll={{ y: 'calc(100vh - 360px)' }}
          pagination={pagigation}
          rowSelection={this.rowSelection}
          loading={this.isLoading}
          onChange={({ sorter: antdSorter }) => {
            this.$emit('changeSort', antdSorter);
          }}
          scopedSlots={{
            companyName: (record) => {
              const name = escape(record.enterpriseName).replace(this.searchKey, `<em>${this.searchKey}</em>`);
              return <QEntityLink class="emphasis" coyObj={{ KeyNo: record.enterpriseId, Name: name }} ellipsis={false}></QEntityLink>;
            },
            personName: (record) => {
              const name = escape(record.personName).replace(this.searchKey, `<em>${this.searchKey}</em>`);
              return <QEntityLink coyObj={{ KeyNo: record.personKeyNo, Name: name }} ellipsis={false}></QEntityLink>;
            },
            actor: (id) => {
              return this.operatorList.find((item) => item.userId === id)?.name || '-';
            },
            action: (record) => {
              return (
                <Button
                  type="link"
                  onClick={() => {
                    this.$router.push({
                      path: `history/detail/${record.preLoanDueId}`,
                    });
                  }}
                >
                  详情
                </Button>
              );
            },
          }}
        />
      </QCard>
    );
  },
});

export default SearchResult;
