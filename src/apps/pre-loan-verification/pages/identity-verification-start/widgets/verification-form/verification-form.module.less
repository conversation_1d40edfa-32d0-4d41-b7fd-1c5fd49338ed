// Token variables
@neutral-1: #333333; /* 0中性/中性1-#333333 */
@neutral-4: #999999; /* 0中性/中性4-#999999 */
@neutral-5: #BBBBBB; /* 0中性/中性5-#BBBBBB */
@neutral-6: #D8D8D8; /* 0中性/中性6-#D8D8D8 */
@neutral-7: #EEEEEE; /* 0中性/中性7-#EEEEEE */
@neutral-9-5: #FAFAFA; /* 0中性/中性9.5-#FAFAFA */
@blue-1: #128BED; /* 1蓝/蓝1常规-#128BED */

.formContainer {
  max-height: 188px;
  overflow-y: auto;
}

.formItem {
  position: relative;
  display: flex;
  margin-bottom: 16px;

  &:not(:first-child) {
    padding-top: 16px;
  }

  & + &::after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: @neutral-7;
  }

  :global {
    .ant-form-inline .ant-form-item-with-help {
      margin-bottom: 0 !important;
    }

    .ant-form-inline .ant-form-item {
      width: calc(33.33% - 10.67px);
      flex-shrink: 0;
    }
  }
}

.singleForm {
  width: 100%;

  &.shrink {
    width: calc(100% - 48px);
  }
}

.deleteIcon {
  flex-shrink: 0;
  margin-top: 12px;
  width: 24px;
  margin-left: 24px;
  color: @neutral-4;

  &:hover {
    color: #808080;
  }
}

.count {
  color: @neutral-4;
}

.buttonContainer {
  display: flex;
  align-items: center;
  gap: 16px;
  border-top: 1px solid @neutral-7;
  padding-top: 16px;

  .addButton {
    &:global(.ant-btn) {
      &:hover, &:focus {
        color: #128BED;
        border-color: currentcolor;
      }
    }
  }
}

.top {
  background: @neutral-9-5;
  padding: 16px;
  margin-top: 16px;
}

.bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.batchButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F2F8FE !important;
}

/**
 * form-wrapper
 */
.formFields {
  display: flex;

  .input {
    width: 100%;
  }

  .lastFormItem {
    //flex: 1;
    display: flex;
    margin-right: 0;

    :global {
      .ant-form-item-control-wrapper {
        flex: 1;
      }
    }
  }

  :global {
    .ant-input,
    .ant-select-lg .ant-select-selection--single {
      height: 36px;
    }

    .ant-select-lg .ant-select-selection__rendered {
      line-height: 34px;
    }

    .ant-form-item-control,
    .ant-form-item-label {
      line-height: 36px;
    }

    .ant-select-selection__placeholder {
      right: 0;
    }

    .ant-select-no-arrow .ant-select-selection-selected-value {
      padding-right: 12px;
    }

    .ant-select-selection__placeholder, .ant-select-search__field__placeholder {
      color: @neutral-5;
    }

    .ant-select-arrow {
      display: none;
    }

    .ant-form-item-control-wrapper {
      width: calc(100% - 66px);
    }
  }
}