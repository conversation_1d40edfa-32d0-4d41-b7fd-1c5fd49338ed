// 文本提示类型
export const StringDetailTypes = [
  'BusinessAbnormal7',
  'FakeSOES',
  'FraudList',
  'NoTender', // 无中标项目
  'NoQualityCertification', // 无有效质量体系认证资质
  'NoCertification', // 无有效关键资质认证
  'QfkRisk',
  'FinancialInstitution', // 重点金融机构
];

export const JsonDetailTypes = [
  'BusinessAbnormal1',
  'BusinessAbnormal6',
  'BusinessAbnormal8',
  'EstablishedTime',
  'LowCapital',
  'FinancialHealth',
];

// 所有权与经营权分离: 嵌套表格，特殊处理
export const NestedTableTypes = ['QfkRisk6802'];

export const notDetailTypes = [
  ...StringDetailTypes,
  ...JsonDetailTypes,
  ...NestedTableTypes,
  'CompanyShell',
  'ContractBreach',
  'NegativeNewsRecent',
  'NegativeNewsHistory',
  'NegativeNews',
  'NoCapital',
  // 实缴异常
  'RealCapitalException',
  /** 清算信息 */
  'Liquidation',
  'QfkRisk6802',
  // 工商基本信息
  'CompanyDetail',
  'QfkRisk6615', // 实际控制人无法识别或穿透边界以外
  'QfkRisk6615', // 实际控制人无法识别或穿透边界以外
  'QfkRisk6302', // 员工数据不明
  'QCCCreditRate', // 企查分
  'GbIndustryCompStatistic',
];

const unitMap = {
  ProvincialHonor: '个',
  TALENT_RECRUIT_STABILITY: '个',
  IntellectualPropertyPledge: '件',
  PatentAnalysis: '件',
  PATENT_APP_STABILITY: '件',
  PATENT_GRANT_STABILITY: '件',
  ScientificAchievement: '件',
  PatentInfoOut: '件',
  InternationPatent: '件',
  AdministrativePenalties: '次',
  RestrictedConsumptionCurrent: '次',
  RiskChange: '次',
  SSERelatedRiskChange: '次',
  PersonCreditCurrent: '次',
};
export const getDimensionUnit = (dimensionKey) => {
  return unitMap[dimensionKey];
};

export const hideCountUnitArr = [
  'EmployeeStockPlatform',
  'EquityFinancing',
  'BusinessAbnormal3',
  'BatchCompanyDetail',
  'TaxArrearsNotice',
  'MainInfoUpdateCapitalChange',
  'PATENT_APP_CONTINUITY',
];
