import { defineComponent, computed, PropType } from 'vue';

import { RiskResultLevelLabelMapping, RiskLevelTextColor, getRiskLevelTextColorName } from '@/config/risk.config';

import styles from './diligence-risk-result-dashboard.module.less';

// 尽调综述
const DiligenceRiskResultDashboard = defineComponent({
  name: 'DiligenceRiskResultDashboard',
  props: {
    list: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    highlightId: {
      type: Number,
      required: false,
    },
  },
  emits: ['modelClick', 'hover'],
  setup(props, { emit }) {
    const showList = computed(() => {
      return props.list.map((v) => {
        const riskLevel = Object.keys(v.details.levelGroup).reduce((acc, cur) => {
          acc[cur] = v.details.originalHits.filter((hit) => +hit.riskLevel === +cur).length;
          return acc;
        }, {});
        return {
          module: v.riskModelName,
          id: v.id,
          riskType: v.result,
          riskName: v.riskName,
          openedCount: v.score,
          ...riskLevel,
        };
      });
    });

    const handleModelClick = (item) => {
      emit('modelClick', item);
    };

    const handleMouseEnter = (item) => {
      emit('hover', item);
    };

    const handleMouseLeave = () => {
      emit('hover', null);
    };

    return {
      showList,
      handleModelClick,
      handleMouseEnter,
      handleMouseLeave,
    };
  },
  render() {
    return (
      <ul class={styles.container}>
        {this.showList.map((item) => {
          return (
            <li
              class={{
                [styles.item]: true,
                [styles.hover]: this.highlightId === item.id,
                [styles[getRiskLevelTextColorName(item.riskType)]]: true,
              }}
              style={{ boxShadow: `inset 5px 0 ${RiskLevelTextColor[item.riskType]}` }}
              onClick={() => this.handleModelClick(item)}
              onMouseenter={() => this.handleMouseEnter(item)}
              onMouseleave={() => this.handleMouseLeave()}
            >
              <div class={styles.riskInfo}>
                <div class={styles.title}>{item.module}</div>
                <div class={styles.risk} style={{ color: RiskLevelTextColor[item.riskType] }}>
                  {item.riskName}
                </div>
              </div>

              <div class={styles.riskMetric}>
                开启模型指标：<i>{item.openedCount}</i>
              </div>

              <div class={styles.divider} />

              <div class={styles.riskStat}>
                {[2, 1, 0].map((level) => {
                  return (
                    <div
                      key={level}
                      class={{
                        [styles.riskLevel]: true,
                        [styles.disabled]: item[level] === 0,
                      }}
                    >
                      <img src={RiskResultLevelLabelMapping[level].icon} class={styles.icon} width="22" height="22" />
                      <span class={styles.title}>{RiskResultLevelLabelMapping[level].label}</span>
                      <span class={styles.count}>{item[level]}</span>
                    </div>
                  );
                })}
              </div>
            </li>
          );
        })}
      </ul>
    );
  },
});

export default DiligenceRiskResultDashboard;
