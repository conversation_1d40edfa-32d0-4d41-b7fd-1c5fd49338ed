import { computed, defineComponent, PropType } from 'vue';
import QRichTable from '@/components/global/q-rich-table';
import { riskColumns } from '../../../../utils/risk-columns.config';
import { getScopedSlots } from '../../../risk-table-next/get-scoped-slots';
import styles from './style.module.less';

const EmployeeStockPlatformTable = defineComponent({
  props: {
    data: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    dimensionKey: {
      type: String,
      required: true,
    },
    dataKey: {
      type: String,
      default: 'personData',
    },
  },
  setup(props) {
    const dataSource = computed(() => props.data[props.dataKey]);
    const oriColumns = riskColumns[props.dimensionKey];
    const columns = computed(() => {
      return [
        {
          title: 'des',
          width: 28,
          customRender: (text, row, index) => {
            const obj = {
              children: (
                <div class={styles.tableDes}>
                  <q-icon class={styles.icon} type="icon-a-shixinxia1x1"></q-icon>
                  <div class={styles.des}>二级股东</div>
                </div>
              ),
              attrs: {
                rowSpan: dataSource.value?.length,
              } as any,
            };
            if (index !== 0) {
              obj.attrs.rowSpan = 0;
            }
            return obj;
          },
        },
        {
          title: '股东名称',
          scopedSlots: {
            customRender: 'companyLowerCamel',
          },
        },
        ...oriColumns.slice(1).map((c, index) => {
          if (index === oriColumns.length - 1) {
            return {
              ...c,
              width: c.width - 1,
            };
          }
          return c;
        }),
      ];
    });
    return {
      columns,
      dataSource,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <QRichTable
          showIndex={false}
          rowKey={'keyNo'}
          bordered={true}
          columns={this.columns}
          dataSource={this.dataSource}
          scopedSlots={{ ...getScopedSlots({ ...this.data.meta }) }}
        />
      </div>
    );
  },
});

export default EmployeeStockPlatformTable;
