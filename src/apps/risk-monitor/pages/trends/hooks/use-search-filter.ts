import { ref, computed, onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router/composables';
import { cloneDeep, isEqual, isNil, sortBy } from 'lodash';

import { useRequest } from '@/shared/composables/use-request';
import { monitor as monitorService } from '@/shared/services';
import { getSearchFilterConfig } from '@/apps/risk-monitor/pages/trends/config/search-config';
import { MetricsTypes, RISK_LEVEL, TrendsStatus } from '@/config/risk.config';

type FilterOptions = {
  label: string;
  value: string | number;
  count?: number;
};

export const useSearchFilter = () => {
  const route = useRoute();

  const monitorGroups = useRequest(monitorService.getAllGroups);
  const aggsOptionSearch = useRequest(monitorService.searchDynamics);
  const searchDynamic = useRequest(monitorService.searchDynamics);

  const defaultFilterValues = Object.freeze({
    keywords: undefined,
    filters: {},
  });

  const isInit = ref(true);

  /** 公司数量汇总 */
  const totalCompany = computed(() => {
    const data = searchDynamic.data.value?.aggsResponse || {};
    return data['4_companyCount']?.value || 0;
  });

  /** 表格数据 */
  const dataSource = computed(() => {
    const data = searchDynamic.data.value || {};
    return data.data || [];
  });

  const pagination = ref({
    pageSize: 10,
    current: 1,
    total: 0,
  });

  const previewQuery = ref<Record<string, any>>({});

  const query = ref<Record<string, any>>(cloneDeep(defaultFilterValues));

  const sort = ref({});

  const isFilterLoading = ref(false);
  const filterOptions = reactive<{
    groupId: FilterOptions[];
    riskLevels: FilterOptions[];
    metricsIds: FilterOptions[];
    metricType: FilterOptions[];
    dataStatus: FilterOptions[];
    primaryObjects: FilterOptions[];
  }>({
    groupId: [],
    riskLevels: [],
    metricsIds: [],
    metricType: [],
    dataStatus: [],
    primaryObjects: [],
  });

  const transformFilterOptions = (aggsData: any[] = [], dict: any[] = [], isNumber = false) => {
    return aggsData.map((item) => {
      const option = dict.find((v) => v.value.toString() === item.key.toString());
      return {
        label: option?.label || '未知',
        value: isNumber ? Number(item.key) : item.key,
        // count: item.doc_count,
      };
    });
  };

  /** 更新搜索选项卡 */
  const getFilterOptions = (group?, showAll = false) => {
    if (showAll) {
      isFilterLoading.value = true;
      return;
    }
    const data = searchDynamic.data.value?.aggsResponse || {};
    filterOptions.metricsIds =
      data['4_metricsId']?.buckets?.map((v) => {
        const label = v.metricsName?.buckets?.[0]?.key;
        return { label, value: v.key };
      }) || [];
    filterOptions.riskLevels = sortBy(transformFilterOptions(data['4_riskLevel']?.buckets, RISK_LEVEL), (o) => -o.value);
    filterOptions.metricType = transformFilterOptions(data['4_metricsType']?.buckets, MetricsTypes);
    filterOptions.dataStatus = transformFilterOptions(data['4_dataStatus']?.buckets, TrendsStatus, true);
    filterOptions.primaryObjects = [
      { label: '企业主体', value: 1, count1: data['4_relatedCompany_exist_stats']?.buckets?.no_relatedCompany?.doc_count || 0 },
      { label: '关联方', value: 2, count1: data['4_relatedCompany_exist_stats']?.buckets?.has_relatedCompany?.doc_count || 0 },
    ].filter((v) => v.count1 > 0);
  };
  /** 搜索过滤配置 */
  const filterGroups = computed(() => {
    return getSearchFilterConfig(filterOptions);
  });

  const search = async (payload?: Record<string, any>) => {
    const res = await searchDynamic.execute({
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
      aggsField: [4],
      ...query.value.filters,
      keywords: query.value.keywords,
      groupId: query.value?.filters?.groupId ? [query.value?.filters?.groupId] : undefined,
      createDate: query.value.filters?.createDate ? [query.value.filters.createDate] : undefined,
      ...sort.value,
      ...payload,
    });
    previewQuery.value = cloneDeep(query.value);
    pagination.value.total = res?.total > 50000 ? 50000 : res?.total;
    pagination.value.current = res?.pageIndex;
    pagination.value.pageSize = res?.pageSize;
  };

  /** groupId改变时，重置其他筛选项 */
  const resetOtherFilters = (remainedKeys: string[], currentFilters = {}) => {
    const filters = remainedKeys.reduce((acc, key) => {
      acc[key] = currentFilters[key];
      return acc;
    }, {});
    return {
      ...defaultFilterValues,
      filters,
    };
  };

  /** 更新搜索过滤 */
  const handleFilterChange = async (values) => {
    const { filters } = values;
    let updateOptions = false;
    if (!isEqual(filters?.groupId, previewQuery.value.filters?.groupId)) {
      query.value = resetOtherFilters(['groupId'], filters);
      updateOptions = true;
    } else {
      query.value = values;
    }
    pagination.value.current = 1;
    await search();
    if (updateOptions) {
      getFilterOptions();
      isFilterLoading.value = false;
    }
  };

  /** 重置搜索过滤 */
  const handleFilterReset = () => {
    query.value = {
      ...defaultFilterValues,
    };
  };

  const initFilter = async () => {
    const queryMap = {
      metricsIds: (val) => val.toString().split(','),
      metricTypes: (val) => [Number(val)],
      dataStatus: (val) => val.toString().split(',').map(Number),
      createDate: (val) => JSON.parse(val.toString()),
      groupId: (val) => [Number(val)],
    };

    const filters = { ...query.value.filters };
    Object.keys(queryMap).forEach((k) => {
      const val = route.query?.[k];
      if (val && isNil(filters[k])) {
        filters[k] = queryMap[k](val);
      }
    });
    query.value = {
      ...query.value,
      filters,
    };

    await search({ aggsField: [4] });
    getFilterOptions();
    isInit.value = false;
  };

  const getAggsGroups = () => {
    const aggsData = aggsOptionSearch.data.value?.aggsResponse || {};
    const allGroups = monitorGroups.data.value?.data || [];
    const groupOptions = sortBy(
      aggsData['4_monitorGroupId']?.buckets?.map((v) => {
        const item = allGroups.find((g) => g.monitorGroupId === +v.key) || {};
        return {
          label: item.name,
          value: item.monitorGroupId,
          count: v.doc_count,
        };
      }) || [],
      (item) => {
        return item.label === '默认分组';
      }
    ).reverse();
    query.value.filters.groupId = groupOptions?.[0]?.label === '默认分组' ? groupOptions[0].value : undefined;
    filterOptions.groupId = [
      ...groupOptions,
      {
        label: '全部',
        value: undefined,
      },
    ];
  };

  /** 初始化 */
  onMounted(async () => {
    try {
      await aggsOptionSearch.execute<any>({ aggsField: [4] });
      await monitorGroups.execute<any>({ pageIndex: 1, pageSize: 100 });
      getAggsGroups();
      await initFilter();
    } catch (error) {
      console.error(error);
    }
  });

  return {
    filterValues: query,
    filterGroups,
    handleFilterChange,
    handleFilterReset,
    isFilterLoading,
    isLoading: searchDynamic.isLoading,
    getFilterOptions,
    totalCompany,
    dataSource,
    pagination,
    search,
    isInit,
    sortInfo: sort,
  };
};
