import { computed, defineComponent, PropType, ref, watch } from 'vue';
import { isNil } from 'lodash';

import styles from './risk-filter.module.less';

const RiskFilter = defineComponent({
  name: 'RiskFilter',
  props: {
    tabs: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    defaultActiveKey: {
      type: Number,
      default: -1,
    },
  },
  setup(props, { emit }) {
    const finalTabs = computed(() => {
      const prefix = [{ name: '全部', groupId: -1 }];
      const data =
        props.tabs?.map((v: any) => ({
          name: v.groupDefinition.groupName,
          groupId: v.groupDefinition.groupId,
          totalHits: v.totalHits,
        })) || [];
      return data.length > 0 ? prefix.concat(data) : [];
    });

    const selectIndex = ref(props.defaultActiveKey);
    const updateSelect = (key: number) => {
      if (key === selectIndex.value) {
        return;
      }
      selectIndex.value = key;
      emit('change', key);
    };

    watch(
      () => props.defaultActiveKey,
      (newVal) => {
        updateSelect(newVal);
      }
    );

    return {
      finalTabs,
      selectIndex,
      updateSelect,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.itemList}>
          {this.finalTabs.map((v: any) => {
            return (
              <div
                class={{ [styles.item]: true, [styles.active]: this.selectIndex === v.groupId }}
                onClick={() => {
                  this.updateSelect(v.groupId);
                }}
              >
                {v.name}
                {isNil(v.totalHits) ? null : <span class={styles.count}>{v.totalHits}</span>}
              </div>
            );
          })}
        </div>
        <div>{this.$slots.extra}</div>
      </div>
    );
  },
});

export default RiskFilter;
