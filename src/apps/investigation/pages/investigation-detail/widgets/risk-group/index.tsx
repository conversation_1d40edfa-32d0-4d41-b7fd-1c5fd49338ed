import { computed, defineComponent, inject, PropType, Ref } from 'vue';
import { useRoute } from 'vue-router/composables';
import { intersection, isArray, orderBy } from 'lodash';

import QIcon from '@/components/global/q-icon';
import { RiskResultLevelLabelMapping } from '@/config/risk.config';
import { useExpandKeys } from '@/apps/investigation/pages/investigation-detail/hook/use-expand-keys';
import RiskHitReasonWrapper from '@/shared/components/risk-hit-reason-wrapper';

import DynamicCollapseBlock from '../dynamic-collapse-block';
import styles from './risk-group.module.less';

const SOURCE_LIST = ['record', 'statistics', 'assessment-batch', 'risk-trends'];

const RiskGroup = defineComponent({
  name: 'RiskGroup',
  props: {
    timeStamp: {
      type: [String, Number],
      default: '',
    },
    // 展示的数据
    diligenceData: {
      type: Object as PropType<{
        groupDefinition: Record<string, any>;
        totalHits: number;
        scoreDetails: Record<string, any>[];
        score: number;
      }>,
      default: () => ({}),
    },
    diligenceInfo: {
      type: Object as PropType<{ companyName: string; keyNo: string; snapshotId: string }>,
      default: () => ({}),
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    // 是否显示分数
    showScore: {
      type: Boolean,
      default: true,
    },
    /** 风险排查结果 */
    riskInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const route = useRoute();
    const from = computed(() => (SOURCE_LIST.includes(route.query?.from as string) ? 'record' : ''));

    // 是不是科创模型
    const isTechModel = inject('isTechModel') as Ref<Record<string, any>>;
    const dataSource = computed(() => {
      const normalizedData = isArray(props.diligenceData.scoreDetails)
        ? props.diligenceData.scoreDetails.map(
            (v) =>
              ({
                ...v,
                score: v.hitDetails.hitStrategy.scoreSettings.maxScore,
                riskLevel: v.hitDetails.hitStrategy.scoreSettings.riskLevel,
              }) as any
          )
        : [];
      const sortKeyList = isTechModel.value ? ['order', 'asc'] : ['score', 'desc'];
      const sortedData = orderBy(normalizedData, ...sortKeyList);
      return sortedData;
    });

    const { expandKeys, onToggleExpand } = useExpandKeys();

    const riskModelDimensionStrategies = inject('riskModelDimensionStrategies') as Ref<Record<string, any>>;
    const allDimensionStrategies = computed(() => {
      const currentModelId = props.riskInfo.orgModelId;
      const currentModel = riskModelDimensionStrategies.value?.[currentModelId] ?? {};
      const strategies = currentModel?.strategies ?? [];
      return strategies;
    });

    return {
      expandKeys,
      isTechModel,
      onToggleExpand,
      from,
      dataSource,
      allDimensionStrategies,
    };
  },

  render() {
    const { showScore } = this;
    const renderHeaderScore = (detail, riskLevel) => {
      if (this.isTechModel) {
        return [
          <div class={[styles.scoreWrapper, styles.techScoreWrapper]}>
            <span class={styles.score}>{detail.hitDetails.hitStrategy.scoreSettings.maxScore}分</span>
          </div>,
          <span class={[styles.text, styles.name]}>{detail.name}</span>,
        ];
      }
      return [
        showScore ? (
          <div class={[styles.scoreWrapper, styles[`level${riskLevel}`]]}>
            <img src={RiskResultLevelLabelMapping[riskLevel].icon} class={styles.icon} width="22" height="22" />
            <span class={styles.score}>{detail.hitDetails.hitStrategy.scoreSettings.maxScore}分</span>
          </div>
        ) : null,
        <span class={[styles.text, styles.name]}>{detail.name}</span>,
        <span class={styles.text}>{detail.totalHits}</span>,
      ];
    };
    return (
      <div class={styles.container}>
        <div class={styles.groupHeader} v-show={this.showTitle}>
          <span>{this.diligenceData.groupDefinition.groupName}</span>
          {/* 科创模型和一般模型取值不一致 */}
          <span>{this.isTechModel ? this.diligenceData.score : this.diligenceData.totalHits}</span>
        </div>
        <div class={styles.groupContent}>
          {this.dataSource.map((detail) => {
            const metricsId = detail.metricsId.toString();
            const containKeys = intersection(Object.keys(detail.hitDetails), ['should', 'must', 'must_not']);
            const detailData = containKeys.map((key) => detail.hitDetails[key]);
            const metaList = detailData.flat().map((v) => ({
              source: v.source,
              key: v.dimensionKey,
              strategyId: v.strategyId,
              description: v.description,
              strategyName: v.strategyName,
              dimensionName: v.dimensionName,
              totalHits: v.totalHits,
              ...this.diligenceInfo,
              tkey: `${Date.now()}_${v.dimensionKey}`,
            }));
            return (
              <DynamicCollapseBlock
                key={metricsId}
                dimensionStrategies={this.allDimensionStrategies}
                id={metricsId}
                metaList={metaList}
                hitDetail={detail}
                defaultCollapse={!this.expandKeys.includes(metricsId) || detail.totalHits === 0}
                onToggle={(isCollapse) => this.onToggleExpand(isCollapse, metricsId)}
                scopedSlots={{
                  header: (isCollapse) => {
                    const riskLevel = detail.hitDetails.hitStrategy.scoreSettings.riskLevel;
                    return (
                      <div
                        class={{
                          [styles.collapseHeader]: true,
                          [styles.active]: !isCollapse,
                          [styles.collapseDisabled]: !detail.totalHits,
                        }}
                      >
                        <QIcon
                          class={{ [styles.arrow]: true, [styles.active]: !isCollapse }}
                          type={isCollapse ? 'icon-a-shixinyou1x' : 'icon-a-shixinxia1x'}
                        />

                        {renderHeaderScore(detail, riskLevel)}

                        <RiskHitReasonWrapper
                          placement="right"
                          hitDetails={detail.hitDetails}
                          dimensionStrategies={this.allDimensionStrategies}
                        />
                      </div>
                    );
                  },
                }}
              ></DynamicCollapseBlock>
            );
          })}
        </div>
      </div>
    );
  },
});

export default RiskGroup;
