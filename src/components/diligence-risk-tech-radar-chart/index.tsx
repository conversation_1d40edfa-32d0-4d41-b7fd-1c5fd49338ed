import { defineComponent, ref, onMounted, watch } from 'vue';
import IconStarFull from './assets/images/star-full.svg';
import IconStartEmpty from './assets/images/star-empty.svg';
import IconStartHalf from './assets/images/star-half.svg';
import QChart from '../global/q-chart';

interface RadarData {
  name: string;
  value: number[];
}

interface RadarIndicator {
  name: string;
  max: number;
  color?: string;
}

/**
 * 计算星级评分
 * @param score 实际分数
 * @param min 最小值
 * @param max 最大值
 * @returns 返回0-100的评分百分比
 */
const calculateStarRating = (score: number, min: number, max: number) => {
  // 计算分数范围
  const range = max - min;
  // 计算实际分数相对于最小值的偏移量
  const scoreRange = score - min;
  // 计算分数在范围内的百分比
  const scorePercent = scoreRange / range;
  // 转换为0-100的评分
  const rating = scorePercent * 100;
  return rating;
};

/**
 * 根据评分计算星级显示
 * @param rating 评分（0-100）
 * @returns 返回包含5个元素的数组，每个元素表示对应星星的状态：1为满星，0.5为半星，0为空星
 */
const getStarsByScore = (rating: number): number[] => {
  // 边界处理：评分小于等于0时返回全空星
  if (rating <= 0) {
    return [0, 0, 0, 0, 0];
  }

  // 边界处理：评分大于100时按满星处理
  const normalizedRating = Math.min(rating, 100);

  // 计算星级：每20分为一颗满星
  const fullStars = Math.floor(normalizedRating / 20);
  // 计算是否有半星：剩余分数大于等于10分时显示半星
  const hasHalfStar = normalizedRating % 20 >= 10;

  // 使用Array.from生成星级数组，避免循环
  return Array.from({ length: 5 }, (_, index) => {
    if (index < fullStars) {
      return 1; // 满星
    } else if (index === fullStars && hasHalfStar) {
      return 0.5; // 半星
    } else {
      return 0; // 空星
    }
  });
};

const generateChartOption = (data, indicator) => {
  return {
    title: undefined,
    legend: undefined,
    tooltip: undefined,
    color: ['rgb(18, 139, 237)', 'rgb(101, 120, 15)', 'rgb(166, 81, 241)'],
    radar: {
      indicator: indicator,
      radius: '55%',
      shape: 'polygon',
      nameGap: 10,
      // 维度标签名称
      axisName: {
        show: true,
        color: '#333',
        fontSize: 11,
        formatter(name: string, { min, max, value }) {
          const startRating = calculateStarRating(value, min, max);
          const stars = getStarsByScore(startRating);
          const starsText = stars
            .map((n) => {
              const map = {
                '0': 'iconEmpty',
                '0.5': 'iconHalf',
                '1': 'iconFull',
              };
              return `{${map[n]}|}`;
            })
            .join('');
          return `{text|${name}}\n${starsText}`;
        },
        rich: {
          text: {
            fontSize: 11,
            lineHeight: 15,
            color: '#333',
          },
          // 星标
          iconFull: {
            width: 10,
            height: 10,
            backgroundColor: {
              image: IconStarFull,
            },
          },
          // 空星标
          iconEmpty: {
            width: 10,
            height: 10,
            backgroundColor: {
              image: IconStartEmpty,
            },
          },
          // 半星标
          iconHalf: {
            width: 10,
            height: 10,
            backgroundColor: {
              image: IconStartHalf,
            },
          },
        },
      },
      splitNumber: 5,
      splitArea: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          color: '#bbb',
          width: 0.52,
          type: 'dashed',
        },
      },
      splitLine: {
        lineStyle: {
          color: '#d8d8d8',
          width: 0.52,
          type: 'solid',
        },
      },
    },
    series: [
      {
        name: '风险数据',
        type: 'radar',
        symbol: 'circle',
        symbolSize: 4,
        // colorBy: 'series',
        data: data.map((item) => ({
          value: item.value,
          name: item.name,
          areaStyle: {
            opacity: 0.05,
          },
          lineStyle: {
            width: 1,
          },
        })),
      },
    ],
  };
};

const DiligenceRiskTechRadarChart = defineComponent({
  name: 'DiligenceRiskTechRadarChart',
  props: {
    title: {
      type: String,
      default: '风险雷达图',
    },
    indicator: {
      type: Array as () => RadarIndicator[],
      default: () => [
        // { name: '发展能力', min: -5, max: 22 },
        // { name: '技术规模', min: 0, max: 10 },
        // { name: '技术能力', min: 0, max: 6 },
        // { name: '技术质量', min: 0, max: 41 },
        // { name: '技术稳定性', min: -5, max: 17 },
        // { name: '运营能力', min: -35, max: 15 },
      ],
    },
    data: {
      type: Array as () => RadarData[],
      default: () => [
        // {
        //   name: '该企业',
        //   value: [85, 65, 45, 75, 60, 80],
        // },
        // {
        //   name: '全国行业水平',
        //   value: [70, 60, 50, 65, 55, 70],
        // },
        // {
        //   name: '同省行业平均',
        //   value: [90, 80, 70, 85, 75, 90],
        // },
      ],
    },
    height: {
      type: String,
      default: '100%',
    },
    width: {
      type: String,
      default: '100%',
    },
  },
  setup(props) {
    const chartOption = ref({});

    const updateChart = () => {
      chartOption.value = generateChartOption(props.data, props.indicator);
    };

    onMounted(() => {
      updateChart();
    });

    watch(
      () => [props.data, props.indicator, props.title],
      () => {
        updateChart();
      },
      { deep: true }
    );

    return {
      chartOption,
    };
  },
  render() {
    return <QChart option={this.chartOption} width={this.width} height={this.height} />;
  },
});

export default DiligenceRiskTechRadarChart;
