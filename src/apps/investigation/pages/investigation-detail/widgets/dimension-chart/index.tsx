import { defineComponent, PropType } from 'vue';

import styles from './dimension-chart.module.less';
import Pie<PERSON>hart from './components/pie-chart';
import LineChart from './components/line-chart';
import { PatentMetricsMap } from './config';
import LineBlock from './components/line-block';

const DimensionChart = defineComponent({
  name: 'DimensionChart',
  props: {
    data: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    dimensionName: {
      type: String,
      default: '',
    },
    displayKey: {
      type: String,
      default: '',
    },
  },
  render() {
    const chartType = PatentMetricsMap[this.displayKey].chartType;
    const getCurrentComp = (chartType) => {
      if (chartType === 'line') {
        return LineChart;
      } else if (chartType === 'pie') {
        return PieChart;
      }
      return LineBlock;
    };

    const ChartComponent = getCurrentComp(chartType);
    return (
      <div class={styles.container}>
        <div class={styles.chartName}>{this.dimensionName}</div>
        <ChartComponent
          is={getCurrentComp(chartType)}
          value={this.data}
          chartTitle={this.dimensionName}
          onChartClick={(data) => {
            this.$emit('chartClick', data);
          }}
        />
      </div>
    );
  },
});

export default DimensionChart;
