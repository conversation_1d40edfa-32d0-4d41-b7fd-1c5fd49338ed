import { isArray } from 'lodash';
import moment from 'moment';

import { HttpClient } from '@/utils/http-client';

function toPascalCase(str: string) {
  if (!str.includes('_')) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
  return str
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

export const createService = (httpClient: HttpClient) => ({
  // 行政许可二合一
  aco(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-aco-detail', params);
  },
  // 公示催告
  async pn(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getPublicNoticeDetail', { params });
    return {
      Detail: res,
    };
  },
  // 业务竞争
  competitor(params): Promise<Readonly<any>> {
    return httpClient.post('/tender/getCompetitor', params);
  },
  // 被执行人详情
  zhixing(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-zhixing-detail', params);
  },
  // 失信被执行人详情
  shixin(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-shixin-detail', params);
  },
  // 股权出质详情
  pledge(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-pledge-detailV2', params);
  },
  // 股东标签点击的股权出质
  gdPledges(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/company/detail/get-detail', params).then((res) => {
      const pledgeList = res?.Pledge ?? [];
      let result = [];
      if (pledgeList?.length) {
        result = pledgeList.filter((item) => item.Status === '有效' && item.PledgorInfo?.KeyNo === params.personId);
      }
      return {
        Result: result,
      };
    });
  },
  // 股权质押详情
  spledge(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-detail-pledgeV2', params);
  },
  //  其他行政处罚
  otherPunish(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-admin-penalty-detail', params);
  },
  // 税务来源行政处罚
  taxPunish(params) {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-admin-penalty-detail', params);
  },
  // 信用中国行政处罚
  xzcfPunish(params) {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-admin-penalty-detail', params);
  },
  // 反垄断行政处罚
  antitrustPunish(params) {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-admin-penalty-detail', params);
  },
  //  工商新政处罚
  gongshangPunish(params) {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-admin-penalty-detail', params);
  },
  // 获取终本案件详情
  zhongben(params: any): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-end-execution-case-detail', params);
  },
  // 获取司法案件详情
  caseDetail(params: any): Promise<Readonly<any>> {
    return httpClient.get('/proxy/api/QccDetail/JudicialCase/Detail', {
      params,
    });
  },
  // 获取询价评估详情
  inquiryDetail(params: any): Promise<Readonly<any>> {
    return httpClient.get('/proxy/api/QccDetail/InquiryEvaluation/Detail', {
      params,
    });
  },
  // 获取限制高消费详情
  xiangaoDetail(params: any): Promise<Readonly<any>> {
    return httpClient.get('/proxy/api/Court/GetSumptuaryDetail', {
      params,
    });
  },
  // 获取详细法院公告
  async gonggao(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getCaseListById', { params });
    return {
      Detail: res,
    };
  },
  // 欠税公告
  owenotice(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-tax-notice-detail', params);
  },
  // 未准入境
  notallowedentry(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },

  // 商业特许经营详情
  sytxjy(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-business-management-detail', params);
  },

  // 股权链详情
  getBenefitDetail(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-benefit-detail', params);
  },

  // 开庭公告详情
  ktnotice(params): Promise<Readonly<any>> {
    return httpClient.get('/company/getCourtNoticeDetail', { params }).then((res) => {
      // 下划线转大驼峰，原本字段不变
      const Result = res?.Result ?? {};
      Object.keys(Result).forEach((key) => {
        Result[toPascalCase(key)] = Result[key];
      });
      return { Result, res };
    });
    // return httpClient.post('/v1/api/dimension/lawsuit/get-court-notice-detail', params);
  },
  // 送达公告详情
  async dnotice(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getDeliveryNoticeDetail', { params });
    return {
      Detail: res,
    };
  },
  // 股权冻结详情
  assistance(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-assistance-detail', params);
  },
  // 环保处罚详情
  env(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-env-penalty-detail', params);
  },
  // 资质证书详情
  zhengshuView(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-certification-detail', params);
  },
  // 税收违法
  taxIllegal(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-tax-detail', params);
  },
  // 动产抵押
  mPledge(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 破产重整详情
  bankruptcy(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-bank-ruptcy-detail', params);
  },
  // 立案信息、诉前调解 详情
  lian(params): Promise<Readonly<any>> {
    return httpClient.get('/company/getDetailOfLiAn', { params });
  },
  // 同系列案件
  sameCaseList(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/lawsuit/get-series-case-list', params);
  },
  // 土地抵押
  async landmortgage(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/get-land-mortgage-detail', { params });
    return {
      Detail: res,
    };
  },
  // 劳动仲裁
  ldzcDetail(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/gov-supervision/get-labor-arbitration-detail', params);
  },
  // 进出口信用
  ciax(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-import-export-detail', params);
  },
  // 地块公示
  landpub(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-land-merge-publicity-detail', params);
  },
  // 购地信息
  landpurchase(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-land-merge-detail', params);
  },
  // 土地转让
  landmarket(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-land-market-deal-detail', params);
  },
  // 违约历程
  bond(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Bond/Detail', {
      params,
    });
  },
  // 电信许可
  telecom(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/enterprise-qualification/get-telecom-license-detail', params);
  },
  // 广告审查
  advercheck(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/manage-develop/get-adverCheck-detail', params);
  },
  // 简易注销
  jyzx(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-simple-cancellation-detail', params);
  },
  // 破产重整研报详情
  announcementDetail(params) {
    return httpClient.get('/company/BankRuptcy/AnnouncementDetail', {
      params,
    });
  },
  // 获取裁判文书详情
  getJudgeDetailById(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-judgement-detail', params);
  },
  // 司法拍卖详情
  getJudicialSaleDetail(params) {
    return httpClient.post('/v1/api/dimension/lawsuit/get-judicial-sale-detail', params);
  },
  // 全球参控股企业详情
  oversea(params) {
    return httpClient.post('/v1/api/dimension/basic-info/get-oversea-control-detail', params);
  },
  // 竞争对手详情
  competitor2(params) {
    return httpClient.post('/v1/api/dimension/commercial-activity/get-competition-detail', params);
  },
  // 股东变更详情
  partnerChange(params) {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 法定代表人
  oper(params) {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 企业名称变更
  entNameChange(params) {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 企业地址变更
  entAddress(params) {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 实际控制人变更
  actualController(params) {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 经营状态变更详情
  operateStatus(params) {
    return httpClient.get('/v1/api/proxy/risk/Risk/GetECIRiskDetail', { params });
  },
  // 询价评估详情
  async inquiryEvaluation(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/Risk/GetRiskDetailV2', { params });
    const caseSearchIds = res?.Detail?.CaseSearchIds;
    if (Array.isArray(caseSearchIds) && caseSearchIds.length > 0) {
      const relatedCasesRes = await httpClient.post('/company/get-case-list-by-ids', {
        ids: caseSearchIds,
      });
      if (Array.isArray(relatedCasesRes?.Result) && relatedCasesRes.Result.length > 0) {
        res.relatedCases = relatedCasesRes.Result;
      }
    }
    return { Result: { ...res, ...res.Detail } };
  },
  // 债券信息详情
  bondInfo(params) {
    return httpClient.post('/v1/api/finance/creditor-rights/get-detail', params);
  },
  // 对外投资股权链
  getInvestmentPathDetail(params): Promise<Readonly<any>> {
    return httpClient.post('/v1/api/dimension/basic-info/get-investment-path-detail', params);
  },

  // 风险各维度通用详情
  commonDetail(params): Promise<Readonly<any>> {
    return httpClient.get(`/group-risk/get-dynamic-detail/${params.riskId}`);
  },
  // 药品抽检
  medicine(params): Promise<Readonly<any>> {
    return httpClient.get('/company/ProductQualityProblem/medicineDetail', { params });
  },
  // 产品抽查
  productChecked(params): Promise<Readonly<any>> {
    return httpClient.get('/company/ProductQualityProblem/productcheckedDetail', { params });
  },
  drc(params): Promise<Readonly<any>> {
    return httpClient.get('/company/ProductQualityProblem/doubleRandomCheckDetail', { params });
  },
  // 失信被执行人详情
  billDefaults(params): Promise<Readonly<any>> {
    return httpClient.post('/company/Risk/BillDefaultDetail', params);
  },
  // 国央企采购黑名单详情接口
  async govProcurementIllegal(params): Promise<Readonly<any>> {
    // 外部黑名单-出口管制合规风险企业清单业务 详情已经返回
    if (params.dimensionKey === 'OvsSanction') {
      return {
        ...params,
        Detail: { Details: JSON.parse(params.Details) },
      };
    }
    return httpClient.get('/company/Risk/GovProcurementIllegal', { params }).then((res) => {
      try {
        const result = res?.Result ?? {};
        result.Details = JSON.parse(result.Details);
      } catch (error) {
        console.error(error);
      }
      return res;
    });
  },
  // 获取终本案件详情接口
  async endExecutionCase(params): Promise<Readonly<any>> {
    try {
      const res = await httpClient.get('/company/Risk/EndExecutionCaseDetail', { params });
      const caseSearchIds = res?.Result?.CaseSearchId;
      if (Array.isArray(caseSearchIds) && caseSearchIds.length > 0) {
        const relatedCasesRes = await httpClient.post('/company/get-case-list-by-ids', {
          ids: caseSearchIds,
        });
        if (Array.isArray(relatedCasesRes?.Result) && relatedCasesRes.Result.length > 0) {
          res.Result.relatedCases = relatedCasesRes.Result;
        }
      }
      return res;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },
  // 受益所有人
  async finalBeneficiary(params) {
    const { Result, Status } = await httpClient.get('/company/Risk/GetECIRiskDetail', { params });
    Result.ChangeDate = Result.ChangeDate ? moment(Result.ChangeDate).format('yyyy-MM-DD') : '-';
    const changeData = Result.ChangeExtend ? JSON.parse(Result.ChangeExtend) : '';
    let before: unknown[] = [];
    let after: unknown[] = [];
    if (changeData?.length > 0) {
      changeData.forEach((element) => {
        const BeforeContent = JSON.parse(element.BeforeContent);
        const AfterContent = JSON.parse(element.AfterContent);
        if (isArray(BeforeContent)) {
          before = [...before, ...BeforeContent];
        } else {
          before.push(BeforeContent);
        }
        if (isArray(AfterContent)) {
          after = [...after, ...AfterContent];
        } else {
          after.push(AfterContent);
        }
      });
      Result.BeforeObject = before;
      Result.AfterObject = after;
    }
    return { Result, Status };
  },

  // 公安通告
  async publicSecurity(params) {
    const { Result } = await httpClient.get('/company/Risk/GetECIRiskDetail', { params });
    const changeData: any = Result.ChangeExtend ? JSON.parse(Result.ChangeExtend) : {};
    return changeData.J;
  },
  // 经营范围变更
  async manageScope(params) {
    const res = await httpClient.get('/company/Risk/GetECIRiskDetail', { params });
    return res;
  },
  // 经营状态变更
  async businessStatus(params) {
    const res = await httpClient.get('/company/Risk/GetECIRiskDetail', { params });
    return res;
  },
  // 注册资本变更
  async registeredCapital(params) {
    const res = await httpClient.get('/company/Risk/GetECIRiskDetail', { params });
    return res;
  },
  // 企业类型
  async entType(params) {
    const res = await httpClient.get('/company/Risk/GetECIRiskDetail', { params });
    return res;
  },
  // 担保信息
  riskGuarantor(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 食品安全
  foodSafetyDetail(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 抽查检查
  spotcheck(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 大股东变更
  bigStockChange(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 抽查检查
  async passportlimit(params) {
    const res = await httpClient.get('/company/Risk/GetRiskDetailV2', { params });
    res.Detail.PublishDate = moment(res.Detail.PublishDate * 1000).format('YYYY-MM-DD');
    return {
      ...res,
      ...res.Detail,
    };
  },
  // 减资公告
  decreaseCapiNotice(data): Promise<Readonly<any>> {
    return httpClient.post('/company/credit/list', data);
  },
  // 【准入排查】税务催缴
  async taxCallNotice(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getTaxDetail', {
      params,
    });
    return { data: { ...res, dimension: 2 } };
  },
  // 【准入排查】税务催报
  async taxReminder(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getTaxDetail', {
      params,
    });
    return { data: { ...res, dimension: 1 } };
  },
  // 【风险动态】税务催缴
  async monitorTaxCall(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getTaxDetail', { params });
    return { data: { ...res, dimension: 2 } };
  },
  // 【风险动态】税务催报
  async monitorTaxReminder(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getTaxDetail', { params });
    return { data: { ...res, dimension: 1 } };
  },
  // 控制企业
  ControlRelation: (data) => {
    return httpClient.post('/bidding/relation2/ControlRelation', data);
  },
  // 股权质押
  stockPledge: (data) => {
    return httpClient.post('/company/get-detail-pledgeV2', data);
  },
  // 对外投资变更
  outboundInvest(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 双随机抽查
  async doubleRandomSampling(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/ProductQualityProblem/doubleRandomCheckDetail', { params });
    return {
      Detail: res?.Result?.[0] || {},
    };
  },
  // 注销备案详情
  enliqDetail(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-list-enliq', params);
  },
  // 双随机抽查
  personShareChange(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 双随机抽查
  partnerDetail(params): Promise<Readonly<any>> {
    return httpClient.get('/company/Risk/GetRiskDetailV2', { params });
  },
  // 产品召回
  async productRecallDetail(params): Promise<Readonly<any>> {
    const res = await httpClient.get('/company/getProductRecallDetail', { params });
    return {
      Detail: res,
    };
  },
  ipoReportPeriod(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-ipoReportPeriod-detail', params);
  },
  ipoOthersPeriod(params): Promise<Readonly<any>> {
    return httpClient.post('/company/get-ipoOthersPeriod-detail', params);
  },
  async benefitDetail(params: { keyNo: string; name: string; toKeyNo: string }): Promise<Readonly<any>> {
    const { Result = {} } = await httpClient.post('/company/getGroupInvPath', params);
    return {
      Result: {
        ...Result,
        isInvest: true,
        isControl: false,
        reverse: false,
        dataType: Result.DataType,
        Paths: Result.Paths.map((paths) => {
          paths.unshift({
            Name: Result.ActualControllerName,
            KeyNo: Result.ActualControllerKeyNo,
          });
          paths.forEach((path, index) => {
            if (index < paths.length - 1) {
              path['underStr'] = paths[index + 1].Percent;
            }
          });
          return paths;
        }),
      },
    };
  },
  tradeDetail(data) {
    return httpClient.post('/company/FinancialInfo/Ashare/TradeDetails', data);
  },
  supplierOrCustomer(data) {
    return httpClient.post('/company/QccSearchListGetSupplierCustomerV2', data);
  },
  async recruitmentAnalysis(params) {
    const detail = await httpClient.get('/company/getRecruitmentDetail', { params });
    if (detail?.Result?.positionName) {
      const jobDetail = await httpClient.post('/company/getRecruitment', {
        searchType: 'job',
        searchKey: detail.Result.positionName,
        sortField: 'salaryavg',
      });
      detail.Result.jobDetail = jobDetail;
    }
    return detail;
  },
});
