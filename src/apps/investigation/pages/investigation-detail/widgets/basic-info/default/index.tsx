import { Popover, Tooltip, message } from 'ant-design-vue';
import { useResizeObserver } from '@vueuse/core';
import { computed, defineComponent, getCurrentInstance, nextTick, ref } from 'vue';

import QCopy from '@/components/global/q-copy';
import { dateFormat } from '@/utils/format';
import { numberToHuman } from '@/utils/number-formatter';
import { copyToClipboard, isTextOverflow, translateRegistCapiLabel } from '@/utils';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { SCALE_SIZE_ICON_MAP } from '@/shared/constants/company-scale-size-icon.constant';
import { getActingList, getOperTypeLabelMapper, getSameList } from '@/utils/firm';
import QIcon from '@/components/global/q-icon';
import QCompanyStatus from '@/components/global/q-company-status';
import QEntityLink from '@/components/global/q-entity-link';
import QPhoneStatus from '@/components/global/q-phone-status';

import styles from './basic-info-default.module.less';
import NationTag from '../widgets/nation-tag';

const ToolTipMap = {
  assetsTotal: {
    0: '来自公开披露的企业资产数据，包含且不仅限于企业工商年报披露的资产数据、企业证券公告披露的合并报表和母公司报表的资产数据（其中在境外上市的境内运营主体数据来自其对应上市公司披露的资产数据）以及其他公开数据来源。',
    1: '基于大数据模型，结合不同行业企业的经营情况数据，建立算法模型分析的企业资产规模可能分布区间。仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。',
  },
  operIncTotal: {
    0: '来自公开披露的企业营业收入，其中上市企业根据上市公告披露的合并报表的营业收入，非上市企业根据最新工商年报披露的营业收入。',
    1: '基于大数据模型，结合不同行业企业的经营情况数据，建立算法模型分析的企业营业收入可能分布区间。仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。',
  },
  netProfit: {
    0: '来自公开披露的企业净利润数据，包含且不仅限于企业工商年报披露的净利润、企业证券公告披露的合并报表和母公司报表的净利润（其中在境外上市的境内运营主体数据来自其对应上市公司披露的净利润）以及其他公开数据来源。',
    1: '基于大数据模型，结合不同行业企业的经营情况数据，建立算法模型分析的企业净利润可能分布区间。仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。',
  },
};

const BasicInfoDefault = defineComponent({
  name: 'BasicInfoDefault',
  props: {
    company: {
      type: Object,
      required: true,
    },
  },
  setup(props, { emit }) {
    const IconType = ref('icon-a-xianduanxia');
    const snapshot = () => {
      emit('snapshot');
    };
    const addressContentRef = ref<Element>();
    const reportAddrRef = ref<Element>();
    const companyName = ref(props.company?.Name || '');
    const needWrap = ref(false); // 是否需要换行
    const key = ref(0);

    const companyInfo = computed(() => {
      return props.company?.ContactInfo ?? {};
    });

    // 如果公司名称过长需要换行显示，第二行开头不能是）
    const bodyWatch = () => {
      companyName.value = props.company?.Name;
      key.value++;
      nextTick(() => {
        const statusDiv = document.getElementsByClassName('qcStatus')[0]?.getClientRects()[0];
        if (statusDiv?.top > 215 && statusDiv?.left >= 205 && statusDiv?.left <= 215) {
          needWrap.value = true;
          const nameLength = props.company?.Name.length;
          const name = props.company?.Name.split('');
          const charCount = name[nameLength - 1] === ')' || name[nameLength - 1] === '）' ? 2 : 1;
          name.splice(nameLength - charCount, 0, '\n');
          companyName.value = name.join('');
        }
      });
    };

    useResizeObserver(document.body, () => {
      setTimeout(() => {
        bodyWatch();
      }, 0);
    });

    const changeIconType = (val) => {
      IconType.value = val ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia';
    };

    const track = useTrack();
    const handleTracker = (btnName) => {
      track(createTrackEvent(6987, '准入排查详情页', btnName, '公司概况'));
    };

    const vm = getCurrentInstance();
    const instance = vm?.proxy as any;

    const handlePhoneSameListClick = (title, field) => {
      const actingList = getActingList(props.company);
      if (field.value && actingList[field.value]) {
        // eslint-disable-next-line no-param-reassign
        field.isActing = true;
      }

      instance.$modal.showDimension('companySamePhone', {
        data: {
          title,
          ...field,
        },
      });
    };

    const handleShowContactMore = (type, list) => {
      if (type === 'email') {
        handleTracker('更多邮箱');
        instance.$modal.showDimension('companyContact', { title: '更多邮箱', size: 'medium' }, { type, list });
      } else if (type === 'tel') {
        handleTracker('更多电话');
        instance.$modal.showDimension(
          'companyContact',
          {
            title: '更多电话',
            size: 'medium',
          },
          {
            type,
            list: list.map((v) => {
              return { t: v.Tel, s: v.SourceFrom };
            }),
            info: props.company,
          }
        );
      }
    };

    const genPhoneField = () => {
      const { company } = props;
      const contentNode: any = [];
      const phone = companyInfo.value?.PhoneNumber;
      if (!phone) {
        return '-';
      }
      contentNode.push(
        <span>
          <QPhoneStatus phone={phone} vtList={company.VTList} /> {phone}
        </span>
      );
      if (company?.HisTelList && company?.HisTelList?.length) {
        contentNode.push(
          <a
            class={styles.fieldPadding}
            onClick={() => {
              handleShowContactMore('tel', company.HisTelList);
            }}
          >
            更多 {company?.HisTelList?.length}
          </a>
        );
      }

      const samePhoneList = getSameList(props.company);
      if (samePhoneList.value?.phone) {
        contentNode.push(
          <a
            class={styles.fieldPadding}
            onClick={() => {
              handlePhoneSameListClick('疑似同电话企业', samePhoneList.value.phone);
              handleTracker('同电话企业');
            }}
          >
            同电话企业 {samePhoneList.value.phone?.count}
          </a>
        );
      }
      return contentNode;
    };

    return {
      IconType,
      changeIconType,

      key,
      companyName,
      needWrap,
      snapshot,
      addressContentRef,
      reportAddrRef,
      companyInfo,
      handleTracker,
      genPhoneField,
    };
  },
  render() {
    const { company } = this;
    let industryInfo = '';
    let copyIndustryInfo = '';
    if (company?.QccIndustry?.Dn) {
      copyIndustryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > ${company?.QccIndustry?.Cn} > ${company?.QccIndustry?.Dn}`;
      industryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > ${company?.QccIndustry?.Cn} > `;
    } else if (company?.QccIndustry?.Cn) {
      copyIndustryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > ${company?.QccIndustry?.Cn}`;
      industryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > `;
    } else {
      copyIndustryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn}`;
      industryInfo = `${company?.QccIndustry?.An} > `;
    }

    const isEmpty = (value) => {
      return !value?.trim() || value?.trim() === '-';
    };
    const isRange = (value) => {
      value = value?.trim();
      return (value?.includes('-') || value?.includes('以内')) && value !== '-';
    };

    const renderOper = () => {
      const { Oper, MultipleOper } = company;
      const renderOps = MultipleOper?.OperList?.length === 1 ? [Oper] : MultipleOper?.OperList;
      return (
        <Tooltip mouseEnterDelay={Oper.Name?.length > 20 ? 0 : 9999} overlayClassName={styles.oper}>
          <div slot="title">
            <QEntityLink coyArr={renderOps}></QEntityLink>
          </div>
          <QEntityLink coyObj={Oper} style={{ maxWidth: '200px' }}></QEntityLink>
        </Tooltip>
      );
    };

    return (
      <div class={styles.companyInfo}>
        <div class={styles.companyTitle}>
          <div class={styles.title}>
            <QCopy
              key={this.key}
              copyValue={company?.Name}
              copyButtonStyle={{
                whiteSpace: 'nowrap',
              }}
            >
              <span class={styles.titleInner} slot="contain">
                <a
                  href={`/embed/companyDetail?keyNo=${company.KeyNo}&title=${company?.Name || ''}`}
                  class={{ [styles.copyTitle]: true, copyContent: true, [styles.needWrap]: this.needWrap }}
                  target="_blank"
                  domPropsInnerHTML={this.companyName || ''}
                  onClick={() => this.handleTracker('公司主页')}
                ></a>
                <QCompanyStatus class="qcStatus" status={company?.ShortStatus} v-show={company?.ShortStatus} ghost />
                <a
                  class={styles.enterButton}
                  href={`/embed/companyDetail?keyNo=${company.KeyNo}&title=${company?.Name || ''}`}
                  target="_blank"
                  onClick={() => this.handleTracker('进入主页')}
                >
                  进入主页
                </a>
              </span>
            </QCopy>
          </div>
          <div class={styles.extra}>{this.$slots.extra}</div>
        </div>
        <NationTag company={company} />
        <div class={styles.companyBasic}>
          <div class={styles.leftBasic}>
            <div class={styles.basicItem}>
              <span class={styles.basiclabel}>统一社会信用代码：</span>
              <span class={styles.basicVal}>
                {company.CreditCode ? (
                  <QCopy
                    copyValue={company.CreditCode}
                    style={{ display: 'flex', height: '22px', alignItems: 'center' }}
                    transparent={false}
                  >
                    <span slot="contain" class={styles.copyValue}>
                      <span class="copyContent">{company.CreditCode}</span>
                    </span>
                  </QCopy>
                ) : (
                  '-'
                )}
              </span>
            </div>
            <div class={styles.basicItem}>
              <span class={styles.basiclabel}>{getOperTypeLabelMapper(company?.MultipleOper?.OperType)}：</span>
              <span class={styles.basicVal}>{renderOper()}</span>
            </div>
            <div class={styles.basicItem}>
              <span class={styles.basiclabel}>
                {translateRegistCapiLabel(company.KeyNo?.startsWith('s') ? 'org' : company.standardCode)}：
              </span>
              <span class={styles.basicVal}>{company.RegistCapi ? numberToHuman(company.RegistCapi) : '-'}</span>
            </div>
            <div class={styles.basicItem} v-show={!isEmpty(company?.assetsTotal)}>
              <span class={styles.basiclabel}>
                资产规模
                <Tooltip placement="bottom">
                  <template slot="title">{ToolTipMap.assetsTotal[Number(isRange(company?.assetsTotal))]}</template>
                  <QIcon type="icon-zhushi" style={{ color: '#D8D8D8' }} />
                </Tooltip>
                ：
              </span>
              <span>
                {company?.assetsTotal}
                {company?.assetsTotalSource ? `(${company.assetsTotalSource})` : ''}
              </span>
            </div>
            <div class={styles.basicItem}>
              <span class={styles.basiclabel}>成立日期：</span>
              <span class={styles.basicVal}>
                <QCopy
                  transparent={false}
                  copyValue={dateFormat(company.StartDate)}
                  style={{ display: 'flex', height: '22px', alignItems: 'center' }}
                >
                  <span slot="contain" class={styles.copyValue}>
                    <span class="copyContent">{dateFormat(company.StartDate)}</span>
                  </span>
                </QCopy>
              </span>
            </div>
          </div>
          <div class={styles.line}></div>
          <div class={styles.centerBasic}>
            <div class={[styles.basicItem, styles.address]}>
              <span class={styles.basiclabel}>{company.Type === 1 ? '住所' : '注册地址'}：</span>
              <span class={styles.basicVal}>
                {company.Address ? (
                  <QCopy
                    transparent={false}
                    copyValue={company.Address}
                    tooltip={isTextOverflow(this.addressContentRef as Element) ? company.Address : false}
                    style={{ display: 'flex', height: '22px', alignItems: 'center' }}
                  >
                    <span slot="contain">
                      <QIcon type="icon-a-zuobiaoxian" style={{ color: '#128bed', verticalAlign: 'middle', marginRight: '4px' }}></QIcon>
                      <span ref="addressContentRef" class={[styles.copyValue, 'copyContent']}>
                        {company.Address}
                      </span>
                    </span>
                  </QCopy>
                ) : (
                  '-'
                )}
              </span>
            </div>
            <div class={[styles.basicItem, styles.address]} v-show={company.Type !== 1}>
              <span class={styles.basiclabel}>经营地址：</span>
              <span class={styles.basicVal}>
                {company.LatestAnnualReportAddrInfo && company.LatestAnnualReportAddrInfo.length
                  ? company.LatestAnnualReportAddrInfo.map((reportAddr) => {
                      return (
                        <span>
                          <QCopy
                            transparent={false}
                            copy-value={`${reportAddr.Address} ${reportAddr.Year ? `(${reportAddr.Year}年)` : ''}`}
                            tooltip={
                              isTextOverflow(this.reportAddrRef as Element)
                                ? `${reportAddr.Address} ${reportAddr.Year ? `(${reportAddr.Year}年)` : ''}`
                                : false
                            }
                            style={{ display: 'inline-flex', height: '22px', alignItems: 'center' }}
                          >
                            <span slot="contain">
                              <QIcon
                                type="icon-a-zuobiaoxian"
                                style={{ color: '#128bed', verticalAlign: 'middle', marginRight: '4px' }}
                              ></QIcon>
                              <span ref="reportAddrRef" class={[styles.smallCopyValue, 'copyContent']}>
                                {reportAddr.Address}
                              </span>
                              <span
                                style={{
                                  display: 'inline-block',
                                  marginRight: '2px',
                                }}
                                class={styles.basiclabel}
                              >{`${reportAddr.Year ? `(${reportAddr.Year}年)` : ''}`}</span>
                            </span>
                          </QCopy>
                        </span>
                      );
                    })
                  : '-'}
              </span>
            </div>
            <div class={styles.basicItem} v-show={company.Type === 1}>
              <span class={styles.basiclabel}>社会组织类型：</span>
              <span class={styles.basicVal}>{company.EconKind || '-'}</span>
            </div>
            <div class={styles.basicItem} v-show={company.Type !== 1}>
              <span class={styles.basiclabel}>实缴资本：</span>
              <span class={styles.basicVal}>{company.RecCap ? numberToHuman(company.RecCap) : '-'}</span>
            </div>
            <div class={styles.basicItem} v-show={!isEmpty(company?.operIncTotal) || company?.CompanyRevenue?.Revenue}>
              <span class={styles.basiclabel}>
                营业收入
                <Tooltip placement="bottom">
                  <template slot="title">
                    {ToolTipMap.operIncTotal[Number(isRange(company?.operIncTotal || company?.CompanyRevenue?.Revenue))]}
                  </template>
                  <QIcon type="icon-zhushi" style={{ color: '#D8D8D8' }} />
                </Tooltip>
                ：
              </span>
              <span>{!isEmpty(company?.operIncTotal) ? company?.operIncTotal : company?.CompanyRevenue?.Revenue}</span>
            </div>
            <div class={styles.basicItem}>
              <span class={styles.basiclabel}>电话：</span>
              <span class={styles.basicVal}>{(this as any).genPhoneField() || '-'}</span>
            </div>
            <div class={styles.basicItem} v-show={company.Type === 1}>
              <span class={styles.basiclabel}>邮箱：</span>
              <span class={styles.basicVal}>
                {company.Email ? (
                  <a href={`mailto:${company.Email}`} target="_blank">
                    {company.Email}
                  </a>
                ) : (
                  '-'
                )}
              </span>
            </div>
          </div>
          <div class={styles.line}></div>
          <div class={styles.rightBasic}>
            <div class={styles.basicItem} v-show={company.Type !== 1}>
              <span class={styles.basiclabel}>所属行业：</span>
              <span class={styles.basicVal}>
                {company?.QccIndustry ? (
                  <Popover
                    placement="bottomRight"
                    overlayClassName={styles.menuPopover}
                    onVisibleChange={(val) => {
                      if (val) {
                        this.handleTracker('所属行业');
                      }
                      this.changeIconType(val);
                    }}
                  >
                    <template slot="content">
                      <div class={styles.industryTextWrapper}>
                        <span class={styles.industryText}>
                          <span style={{ color: '#BBBBBB' }}>{industryInfo}</span>
                          <span style={{ color: '#FFFFFF' }}>
                            {company?.QccIndustry?.Dn || company?.QccIndustry?.Cn || company?.QccIndustry?.Bn || company?.QccIndustry?.An}
                          </span>
                          <span
                            class={styles.copyText}
                            onClick={() => {
                              try {
                                copyToClipboard(copyIndustryInfo);
                                message.success('复制成功');
                              } catch {
                                message.error('复制失败');
                              }
                            }}
                          >
                            <QIcon class={[styles.icon]} type="icon-gongyingshangchouqu" />
                            <span style={{ marginLeft: '4px' }}>复制</span>
                          </span>
                        </span>
                      </div>
                    </template>
                    <span class={styles.industryInfo}>
                      {company?.QccIndustry?.Dn || company?.QccIndustry?.Cn || company?.QccIndustry?.Bn || company?.QccIndustry?.An}
                    </span>
                    <QIcon class={styles.icon} type={this.IconType} />
                  </Popover>
                ) : (
                  '-'
                )}
              </span>
            </div>
            <div class={styles.basicItem} v-show={company?.Scale}>
              <span class={styles.basiclabel}>
                企业规模
                <Tooltip placement="bottom">
                  <template slot="title">
                    基于大数据模型，结合不同行业企业的经营情况数据，建立算法模型，形成的
                    <b>L(大型)、M(中型)、S(小型)</b>和<b>XS(微型)</b>四类规模体系。
                  </template>
                  <QIcon type="icon-zhushi" style={{ color: '#D8D8D8' }} />
                </Tooltip>
                ：
              </span>
              <span class={styles.basicVal}>
                <QIcon type={SCALE_SIZE_ICON_MAP[company?.Scale]} class={styles.scaleIcon} />
                {company?.Scale || '-'}
              </span>
            </div>
            <div class={styles.basicItem} v-show={company?.Staffs?.c}>
              <span class={styles.basiclabel}>
                员工人数
                <Tooltip placement="bottom">
                  <template slot="title">
                    来自公开披露的员工人数，非上市企业根据最新工商年报披露的社保参与人数，上市企业根据上市公告披露的合并报表的员工总数，其中在境外上市的大陆运营实体公司数据来自其对应上市公司披露的员工总数。
                  </template>
                  <QIcon type="icon-zhushi" style={{ color: '#D8D8D8' }} />
                </Tooltip>
                ：
              </span>
              <span>
                {company?.Staffs?.c ? numberToHuman(company?.Staffs?.c) : '-'}
                {company?.Staffs?.c ? '人' : ''}
                {company?.Staffs?.s && company?.Staffs?.c ? <i class={styles.basiclabel}>{`(${company?.Staffs?.s}年)`}</i> : ''}
              </span>
            </div>
            <div class={styles.basicItem} v-show={!isEmpty(company?.netProfit)}>
              <span class={styles.basiclabel}>
                净利润
                <Tooltip placement="bottom">
                  <template slot="title">{ToolTipMap.netProfit[Number(isRange(company?.netProfit))]}</template>
                  <QIcon type="icon-zhushi" style={{ color: '#D8D8D8' }} />
                </Tooltip>
                ：
              </span>
              <span>
                {company?.netProfit}
                {company?.netProfitSource ? `(${company.netProfitSource})` : ''}
              </span>
            </div>
            <div class={styles.basicItem} v-show={company.Type === 1}>
              <span class={styles.basiclabel}>证书有效期：</span>
              <span class={styles.basicVal}>{company.CertDate || '-'}</span>
            </div>
            <div class={styles.basicItem} v-show={company.Type === 1}>
              <span class={styles.basiclabel}>证书状态：</span>
              <span class={styles.basicVal}>{company.CertificateStatus || '-'}</span>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default BasicInfoDefault;
