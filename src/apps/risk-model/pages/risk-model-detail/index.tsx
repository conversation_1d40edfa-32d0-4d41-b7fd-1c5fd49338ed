import { computed, defineComponent, onMounted, PropType, provide, ref } from 'vue';
import { Breadcrumb, Spin, message } from 'ant-design-vue';
import { useRoute } from 'vue-router/composables';

import { setting as settingService } from '@/shared/services';
import { hasPermission } from '@/shared/composables/use-permission';
import { Permission } from '@/config/permissions.config';

import ModelSettingSummary from './widgets/model-setting-summary';
import ModelQuantitativeIndicators from './widgets/model-quantitative-indicators';
import ModelDimensionSettings from './widgets/model-dimenison-settings';
import styles from './risk-model-detail.module.less';
import { useModelDataHook } from '../../hooks/use-data-hook';

const RiskModelDetailPage = defineComponent({
  name: 'RiskModelDetailPage',
  props: {
    /**
     * 是否为嵌入页面
     */
    isExternal: {
      type: Boolean,
      default: false,
    },
    /**
     * 模型类型
     */
    modelType: {
      type: String as PropType<'1' | '2'>,
      default: '1',
    },
  },
  setup(props) {
    const { modelDetail } = useModelDataHook();
    const route = useRoute();
    const loading = ref(false);

    // 获取模型详情
    const getModelSetting = async () => {
      loading.value = true;
      try {
        const res = await settingService.getModelDetail(route.params.riskModelId);
        modelDetail.value = res;
      } catch (error) {
        console.log(error);
      } finally {
        loading.value = false;
      }
    };
    onMounted(() => {
      getModelSetting();
    });

    const isMonitor = computed(() => {
      return modelDetail.value.modelType === 2;
    });

    // 模型是禁用或者废弃状态或者分发关系中是禁用或者废弃状态
    const isDisabled = computed(() => {
      return (
        [0, 4].includes(modelDetail.value.status) ||
        [0, 3].includes(modelDetail.value?.distributedResource?.[0]?.distributeStatus) ||
        !hasPermission(props.modelType === '1' ? [Permission.INVESTIGATION_MODEL_EDIT] : [Permission.MONITOR_MODEL_EDIT])
      );
    });

    provide('isMonitor', isMonitor);

    provide('isDisabled', isDisabled);

    provide('modelDetail', modelDetail);
    const updateModelName = async (name: string) => {
      try {
        await settingService.editNameSetting({
          modelId: modelDetail.value.modelId,
          resultSetting: modelDetail.value.resultSetting,
          comment: modelDetail.value.comment,
          modelName: name,
        });
        message.success('模型名称保存成功');
        modelDetail.value.modelName = name;
      } catch (error) {
        console.log(error);
      }
    };
    return {
      loading,
      isDisabled,
      isMonitor,
      modelDetail,
      updateModelName,
    };
  },
  render() {
    const { modelDetail } = this;
    const homeRoute = (modelType = '1') => {
      const routeMap = {
        1: {
          name: 'investigation-model-list',
          label: '尽调模型',
        },
        2: {
          name: 'risk-monitor-model-list',
          label: '监控模型',
        },
      };
      return (
        <router-link
          to={{
            name: routeMap[modelType].name,
          }}
        >
          <q-icon type="icon-mianbaoxiefanhui" />
          {routeMap[modelType].label}
        </router-link>
      );
    };
    return (
      <div class={styles.container}>
        {!this.isExternal ? (
          <Breadcrumb class="sticky-breadcrumb">
            <Breadcrumb.Item>{homeRoute(this.modelType)}</Breadcrumb.Item>
            <Breadcrumb.Item>{modelDetail.modelName}</Breadcrumb.Item>
          </Breadcrumb>
        ) : null}

        {this.loading ? (
          <Spin class={styles.spin} />
        ) : (
          <div class={styles.content}>
            {/* 模型信息 */}
            <ModelSettingSummary detail={modelDetail} onUpdateName={this.updateModelName} />
            {/* 定量指标 */}
            {modelDetail.modelType !== 2 ? (
              <ModelQuantitativeIndicators
                detail={modelDetail}
                settings={modelDetail.resultSetting}
                onUpdate={(data) => {
                  modelDetail.resultSetting = data;
                }}
              />
            ) : null}
            {/* 模型指标 */}
            <ModelDimensionSettings modelData={modelDetail} />
          </div>
        )}
      </div>
    );
  },
});

export default RiskModelDetailPage;
